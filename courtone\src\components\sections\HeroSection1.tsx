'use client';

import { Button } from '@/components/ui/button';
import dynamic from 'next/dynamic';

// Dynamic import untuk 3D component (client-side only)
const Interactive3DModel = dynamic(() => import('@/components/3d/Interactive3DModel'), {
  loading: () => (
    <div className="w-64 h-64 md:w-80 md:h-80 flex items-center justify-center">
      <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-lime-400"></div>
    </div>
  )
});

export default function HeroSection1() {
  return (
    <section className="bg-black text-white py-16 px-6 md:px-12">
      <div className="max-w-7xl mx-auto flex flex-col md:flex-row items-center">
        <div className="w-full md:w-1/2 mb-10 md:mb-0">
          <div className="flex items-center mb-4">
            <span className="text-xl">📍</span>
            <span className="ml-2">Indonesia</span>
          </div>
          
          <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold leading-tight mb-6">
            THE WORLD'S FIRST BLOCKCHAIN BACKED PADEL COURT
          </h1>
          
          <p className="text-lg md:text-xl mb-8 text-zinc-300">
            Own real courts. Earn passive income. Invest through NFTs and enjoy projected returns of up to 15% APR, backed by actual court revenue.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4">
            <Button className="bg-white text-black hover:bg-zinc-200 font-semibold px-8 py-6">
              Join Whitelist
            </Button>
            <Button variant="outline" className="border-white text-white bg-transparent hover:bg-white hover:text-black font-semibold px-8 py-6">
              Whitepaper
            </Button>
          </div>
        </div>
        
        <div className="w-full md:w-1/2 flex justify-center">
          <div className="relative w-64 h-64 md:w-80 md:h-80">
            <Interactive3DModel />
          </div>
        </div>
      </div>
    </section>
  );
}

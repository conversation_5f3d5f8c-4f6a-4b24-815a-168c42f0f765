{"c": ["app/layout", "app/page", "webpack"], "r": ["_app-pages-browser_src_components_3d_Interactive3DModel_tsx"], "m": ["(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/app-dynamic.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-chunks.js", "(shared)/./node_modules/next/dist/server/app-render/async-local-storage.js", "(shared)/./node_modules/next/dist/server/app-render/work-async-storage-instance.js", "(shared)/./node_modules/next/dist/server/app-render/work-async-storage.external.js", "(app-pages-browser)/./node_modules/@babel/runtime/helpers/esm/extends.js", "(app-pages-browser)/./node_modules/@monogrid/gainmap-js/dist/QuadRenderer-DuOPRGA4.js", "(app-pages-browser)/./node_modules/@monogrid/gainmap-js/dist/decode.js", "(app-pages-browser)/./node_modules/@react-three/drei/core/Clone.js", "(app-pages-browser)/./node_modules/@react-three/drei/core/Environment.js", "(app-pages-browser)/./node_modules/@react-three/drei/core/Gltf.js", "(app-pages-browser)/./node_modules/@react-three/drei/core/OrbitControls.js", "(app-pages-browser)/./node_modules/@react-three/drei/core/useEnvironment.js", "(app-pages-browser)/./node_modules/@react-three/drei/helpers/environment-assets.js", "(app-pages-browser)/./node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js", "(app-pages-browser)/./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js", "(app-pages-browser)/./node_modules/@react-three/fiber/node_modules/scheduler/cjs/scheduler.development.js", "(app-pages-browser)/./node_modules/@react-three/fiber/node_modules/scheduler/index.js", "(app-pages-browser)/./node_modules/its-fine/dist/index.js", "(app-pages-browser)/./node_modules/react-reconciler/cjs/react-reconciler-constants.development.js", "(app-pages-browser)/./node_modules/react-reconciler/cjs/react-reconciler.development.js", "(app-pages-browser)/./node_modules/react-reconciler/constants.js", "(app-pages-browser)/./node_modules/react-reconciler/index.js", "(app-pages-browser)/./node_modules/react-reconciler/node_modules/scheduler/cjs/scheduler.development.js", "(app-pages-browser)/./node_modules/react-reconciler/node_modules/scheduler/index.js", "(app-pages-browser)/./node_modules/react-use-measure/dist/index.js", "(app-pages-browser)/./node_modules/suspend-react/index.js", "(app-pages-browser)/./node_modules/three-stdlib/_polyfill/LoaderUtils.js", "(app-pages-browser)/./node_modules/three-stdlib/_polyfill/constants.js", "(app-pages-browser)/./node_modules/three-stdlib/controls/EventDispatcher.js", "(app-pages-browser)/./node_modules/three-stdlib/controls/OrbitControls.js", "(app-pages-browser)/./node_modules/three-stdlib/libs/MeshoptDecoder.js", "(app-pages-browser)/./node_modules/three-stdlib/loaders/DRACOLoader.js", "(app-pages-browser)/./node_modules/three-stdlib/loaders/EXRLoader.js", "(app-pages-browser)/./node_modules/three-stdlib/loaders/GLTFLoader.js", "(app-pages-browser)/./node_modules/three-stdlib/loaders/RGBELoader.js", "(app-pages-browser)/./node_modules/three-stdlib/node_modules/fflate/esm/browser.js", "(app-pages-browser)/./node_modules/three-stdlib/objects/GroundProjectedEnv.js", "(app-pages-browser)/./node_modules/three-stdlib/types/helpers.js", "(app-pages-browser)/./node_modules/three-stdlib/utils/BufferGeometryUtils.js", "(app-pages-browser)/./node_modules/three-stdlib/utils/SkeletonUtils.js", "(app-pages-browser)/./node_modules/three/build/three.core.js", "(app-pages-browser)/./node_modules/three/build/three.module.js", "(app-pages-browser)/./node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js", "(app-pages-browser)/./node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js", "(app-pages-browser)/./node_modules/use-sync-external-store/shim/index.js", "(app-pages-browser)/./node_modules/use-sync-external-store/shim/with-selector.js", "(app-pages-browser)/./node_modules/zustand/esm/traditional.mjs", "(app-pages-browser)/./node_modules/zustand/esm/vanilla.mjs", "(app-pages-browser)/./src/components/3d/Interactive3DModel.tsx"]}
import { Card, CardContent } from "@/components/ui/card";

interface FeatureCardProps {
  number: string;
  title: string;
  description: string;
  icon: React.ReactNode;
}

function FeatureCard({ number, title, description, icon }: FeatureCardProps) {
  return (
    <Card className="bg-zinc-900 border-zinc-800">
      <CardContent className="p-6">
        <div className="mb-4 text-lime-400">{icon}</div>
        <p className="text-lime-400 font-bold mb-2">{number}</p>
        <h3 className="text-xl font-bold text-white mb-2">{title}</h3>
        <p className="text-zinc-400">{description}</p>
      </CardContent>
    </Card>
  );
}

export default function HowItWorks() {
  return (
    <section className="bg-black text-white py-20 px-6 md:px-12">
      <div className="max-w-7xl mx-auto">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-12 bg-gradient-to-r from-[#d2ff00] to-white bg-clip-text text-transparent">
          HOW DOES COURT ONE WORKS?
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <FeatureCard
            number="01"
            title="YOU INVEST"
            description="Mint a CourtOne NFT to claim your ownership stake"
            icon={
              <img src="/trading-card.png" alt="Trading Card" width="36" height="36" className="object-contain" />
            }
          />
          
          <FeatureCard
            number="02"
            title="WE BUILD"
            description="Premium padel court in high-traffic areas in Indonesia"
            icon={
              <img src="/padel.png" alt="Padel Court" width="36" height="36" className="object-contain" />
            }
          />
          
          <FeatureCard
            number="03"
            title="YOU EARN"
            description="Enjoy passive income with profits distributed monthly"
            icon={
              <img src="/wallet.png" alt="Wallet" width="36" height="36" className="object-contain" />
            }
          />
          
          <FeatureCard
            number="04"
            title="YOU PLAY"
            description="Get community perks and early access to special events"
            icon={
              <img src="/padelicon.png" alt="Padel Icon" width="36" height="36" className="object-contain" />
            }
          />
        </div>
      </div>
    </section>
  );
}

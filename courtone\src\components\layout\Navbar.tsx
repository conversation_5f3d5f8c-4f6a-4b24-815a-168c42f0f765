import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';

export default function Navbar() {
  return (
    <nav className="w-full bg-black text-white py-4 px-6 md:px-12">
      <div className="max-w-7xl mx-auto flex justify-between items-center">
        <Link href="/" className="text-2xl font-bold">
          COURT ONE
        </Link>
        <div className="space-x-4">
          <Button variant="link" className="text-white font-bold">
            WHITEPAPER
          </Button>
          <Button variant="outline" className="text-black bg-white border-white hover:bg-gray-100 hover:text-black transition-colors">
            Dashboard
          </Button>
        </div>
      </div>
    </nav>
  );
}

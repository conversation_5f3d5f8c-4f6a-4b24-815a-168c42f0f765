"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/HeroSection1.tsx":
/*!**************************************************!*\
  !*** ./src/components/sections/HeroSection1.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HeroSection1)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction HeroSection1() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"bg-black text-white py-16 px-6 md:px-12\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto flex flex-col md:flex-row items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full md:w-1/2 mb-10 md:mb-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl\",\n                                    children: \"\\uD83D\\uDCCD\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n                                    lineNumber: 12,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2\",\n                                    children: \"Indonesia\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n                                    lineNumber: 13,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n                            lineNumber: 11,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl md:text-4xl lg:text-5xl font-bold leading-tight mb-6\",\n                            children: \"THE WORLD'S FIRST BLOCKCHAIN BACKED PADEL COURT\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg md:text-xl mb-8 text-zinc-300\",\n                            children: \"Own real courts. Earn passive income. Invest through NFTs and enjoy projected returns of up to 15% APR, backed by actual court revenue.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    className: \"bg-white text-black hover:bg-zinc-200 font-semibold px-8 py-6\",\n                                    children: \"Join Whitelist\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"outline\",\n                                    className: \"border-white text-white bg-transparent hover:bg-white hover:text-black font-semibold px-8 py-6\",\n                                    children: \"Whitepaper\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full md:w-1/2 flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-64 h-64 md:w-80 md:h-80\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Interactive3DModel, {}, void 0, false, {\n                            fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n_c = HeroSection1;\nvar _c;\n$RefreshReg$(_c, \"HeroSection1\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/HeroSection1.tsx\n"));

/***/ })

});
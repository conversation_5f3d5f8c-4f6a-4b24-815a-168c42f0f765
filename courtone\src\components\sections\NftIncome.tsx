import Image from 'next/image';

interface FeatureItemProps {
  title: string;
  description: string;
  iconSrc: string;
}

function FeatureItem({ title, description, iconSrc }: FeatureItemProps) {
  return (
    <div className="flex mb-6">
      <div className="mr-4 flex-shrink-0">
        <Image
          src={iconSrc}
          alt={title}
          width={24}
          height={24}
          className="object-contain"
        />
      </div>
      <div>
        <h4 className="font-bold mb-1 bg-gradient-to-r from-[#d2ff00] to-white bg-clip-text text-transparent">{title}</h4>
        <p className="text-zinc-400">{description}</p>
      </div>
    </div>
  );
}

export default function NftIncome() {
  return (
    <section className="bg-black text-white py-20 px-6 md:px-12">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row items-center">
          <div className="w-full md:w-1/2 mb-10 md:mb-0">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-[#d2ff00] to-white bg-clip-text text-transparent">
              YOUR NFT, YOUR INCOME
            </h2>
            
            <p className="text-xl mb-8">
              Earn up to 15% APR from real padel court revenue
            </p>
            
            <div>
              <FeatureItem
                title="Projected APR"
                description="Up to 15% annually based on actual court rental and operational profits"
                iconSrc="/percent.png"
              />

              <FeatureItem
                title="Revenue Sources"
                description="Court bookings, equipment rentals, private events, and future F&B"
                iconSrc="/coin.png"
              />

              <FeatureItem
                title="On-Chain Reporting"
                description="Transparent earnings via our real-time dashboard"
                iconSrc="/block.png"
              />

              <FeatureItem
                title="Monthly Payouts"
                description="Distributed directly to NFT holders"
                iconSrc="/CAL.png"
              />
            </div>
            
            <p className="text-xs text-zinc-500 mt-6">
              Returns are based on net profit and are subject to operational performance
            </p>
          </div>
          
          <div className="w-full md:w-1/2 flex justify-center">
            <div className="relative w-80 h-80">
              <Image
                src="/motion.gif"
                alt="Motion Animation"
                fill
                className="object-contain"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

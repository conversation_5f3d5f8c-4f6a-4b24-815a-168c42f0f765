"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/HeroSection1.tsx":
/*!**************************************************!*\
  !*** ./src/components/sections/HeroSection1.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HeroSection1)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n// Dynamic import untuk 3D component (client-side only)\nconst Interactive3DModel = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_3d_Interactive3DModel_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/3d/Interactive3DModel */ \"(app-pages-browser)/./src/components/3d/Interactive3DModel.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\sections\\\\HeroSection1.tsx -> \" + \"@/components/3d/Interactive3DModel\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-64 h-64 md:w-80 md:h-80 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-lime-400\"\n            }, void 0, false, {\n                fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n            lineNumber: 9,\n            columnNumber: 5\n        }, undefined)\n});\n_c = Interactive3DModel;\nfunction HeroSection1() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"bg-black text-white py-16 px-6 md:px-12\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto flex flex-col md:flex-row items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full md:w-1/2 mb-10 md:mb-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl\",\n                                    children: \"\\uD83D\\uDCCD\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2\",\n                                    children: \"Indonesia\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl md:text-4xl lg:text-5xl font-bold leading-tight mb-6\",\n                            children: \"THE WORLD'S FIRST BLOCKCHAIN BACKED PADEL COURT\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg md:text-xl mb-8 text-zinc-300\",\n                            children: \"Own real courts. Earn passive income. Invest through NFTs and enjoy projected returns of up to 15% APR, backed by actual court revenue.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    className: \"bg-white text-black hover:bg-zinc-200 font-semibold px-8 py-6\",\n                                    children: \"Join Whitelist\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"outline\",\n                                    className: \"border-white text-white bg-transparent hover:bg-white hover:text-black font-semibold px-8 py-6\",\n                                    children: \"Whitepaper\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full md:w-1/2 flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-64 h-64 md:w-80 md:h-80\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Interactive3DModel, {}, void 0, false, {\n                            fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\sections\\\\HeroSection1.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n_c1 = HeroSection1;\nvar _c, _c1;\n$RefreshReg$(_c, \"Interactive3DModel\");\n$RefreshReg$(_c1, \"HeroSection1\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/HeroSection1.tsx\n"));

/***/ })

});